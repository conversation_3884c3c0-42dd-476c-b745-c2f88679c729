"""
Specialized Spatial Flow Analysis System for Heavy Vehicle and Utility Vehicle OD Data
Strasbourg - EMC2 Zone Grid Analysis

This module implements comprehensive spatial flow analysis with zonal aggregation
for Origin-Destination data, supporting multiple vehicle types and temporal aggregations.
"""

import pandas as pd
import geopandas as gpd
import numpy as np
from pathlib import Path
from dataclasses import dataclass
from typing import Dict, List, Tuple, Optional, Union
from datetime import datetime
import logging
import time
from pyproj import Transformer
from shapely.geometry import Point
import warnings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("spatial_flow_analysis.log"),
        logging.StreamHandler(),
    ],
)
logger = logging.getLogger(__name__)
warnings.filterwarnings("ignore")


@dataclass
class DataConfiguration:
    """Configuration class for data paths and parameters."""

    work_dir: str = r"C:\Users\<USER>\Documents\affaires\23I711.3 Strasbourg OD PL FCD"
    zone_shapefile: str = None
    output_dir: str = "spatial_analysis_output"

    def __post_init__(self):
        if self.zone_shapefile is None:
            self.zone_shapefile = (
                f"{self.work_dir}\\SIG_EMC2\\EMC2_NordAlsace_2024_DTIR_31072024.shp"
            )


@dataclass
class QualityControlConfig:
    """Configuration for data quality control checks."""

    validate_coordinates: bool = True
    validate_travel_times: bool = True
    handle_duplicates: bool = True
    coordinate_bounds: Tuple[float, float, float, float] = (
        7.6,
        7.9,
        48.4,
        48.7,
    )  # min_lon, max_lon, min_lat, max_lat
    max_travel_time_hours: float = 6.0


@dataclass
class ProcessingMetrics:
    """Container for processing metrics and statistics."""

    total_input_records: int = 0
    records_after_quality_control: int = 0
    coordinate_outliers_removed: int = 0
    invalid_travel_times_removed: int = 0
    duplicates_removed: int = 0
    spatial_join_success_rate: float = 0.0
    unmatched_origins: int = 0
    unmatched_destinations: int = 0
    processing_time_seconds: float = 0.0


class DataQualityController:
    """Enhanced data quality control with configurable checks."""

    def __init__(self, config: QualityControlConfig):
        self.config = config
        self.metrics = ProcessingMetrics()

    def validate_coordinates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate coordinate bounds and remove outliers."""
        if not self.config.validate_coordinates:
            return df

        initial_count = len(df)
        min_lon, max_lon, min_lat, max_lat = self.config.coordinate_bounds

        # Filter origin and destination coordinates
        df = df[
            (df["Origine X"] >= min_lon)
            & (df["Origine X"] <= max_lon)
            & (df["Origine Y"] >= min_lat)
            & (df["Origine Y"] <= max_lat)
            & (df["Destination X"] >= min_lon)
            & (df["Destination X"] <= max_lon)
            & (df["Destination Y"] >= min_lat)
            & (df["Destination Y"] <= max_lat)
        ]

        removed_count = initial_count - len(df)
        self.metrics.coordinate_outliers_removed = removed_count
        logger.info(
            f"Coordinate validation: Removed {removed_count} records with invalid coordinates"
        )
        return df

    def validate_travel_times(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate travel times and remove unrealistic values."""
        if not self.config.validate_travel_times:
            return df

        initial_count = len(df)
        max_minutes = self.config.max_travel_time_hours * 60

        # Remove negative travel times and extremely long trips
        df = df[
            (df["travel_time_minutes"] > 0) & (df["travel_time_minutes"] <= max_minutes)
        ]

        removed_count = initial_count - len(df)
        self.metrics.invalid_travel_times_removed = removed_count
        logger.info(
            f"Travel time validation: Removed {removed_count} records with invalid travel times"
        )
        return df

    def handle_duplicates(self, df: pd.DataFrame) -> pd.DataFrame:
        """Handle duplicate records based on business rules."""
        if not self.config.handle_duplicates:
            return df

        initial_count = len(df)

        # Keep first occurrence of duplicate vehicle IDs with same timestamp
        df = df.drop_duplicates(subset=["ID", "datetime"], keep="first")

        removed_count = initial_count - len(df)
        self.metrics.duplicates_removed = removed_count
        logger.info(f"Duplicate handling: Removed {removed_count} duplicate records")
        return df

    def get_metrics(self) -> ProcessingMetrics:
        """Get processing metrics."""
        return self.metrics

class SpatialFlowProcessor:
    """Main processor for spatial flow analysis with zonal aggregation."""

    def __init__(
        self,
        vehicle_type: str,
        input_csv_path: str,
        config: DataConfiguration,
        quality_config: QualityControlConfig = None,
    ):
        self.vehicle_type = vehicle_type
        self.input_csv_path = input_csv_path
        self.config = config
        self.quality_config = quality_config
        self.quality_controller = DataQualityController(quality_config) if quality_config else None
        self.transformer = Transformer.from_crs(
            "EPSG:4326", "EPSG:2154", always_xy=True
        )

        # Create output directory
        self.output_path = Path(config.output_dir)
        self.output_path.mkdir(exist_ok=True)

        # Initialize data containers
        self.raw_data = None
        self.clean_data = None
        self.zone_data = None
        self.zonal_flows = None

    def load_and_preprocess_data(self) -> pd.DataFrame:
        """Load and preprocess OD data with quality controls."""
        start_time = time.time()

        try:
            logger.info(f"Loading {self.vehicle_type} data from {self.input_csv_path}")

            # Read CSV with proper encoding
            df = pd.read_csv(self.input_csv_path, sep=";", encoding="utf-8")

            # Initialize metrics tracking
            if self.quality_controller:
                self.quality_controller.metrics.total_input_records = len(df)

            # Parse datetime columns
            df["datetime"] = pd.to_datetime(
                df["Date"] + " " + df["Heure"], format="%d/%m/%Y %H:%M"
            )
            df["origin_datetime"] = pd.to_datetime(df["Horodate origine"])
            df["destination_datetime"] = pd.to_datetime(df["Horodate destination"])

            # Calculate travel time
            df["travel_time_minutes"] = (
                df["destination_datetime"] - df["origin_datetime"]
            ).dt.total_seconds() / 60

            # Add temporal features
            df["hour"] = df["datetime"].dt.hour
            df["day_of_week"] = df["datetime"].dt.day_name()
            df["weekday_num"] = df["datetime"].dt.weekday  # 0=Monday, 6=Sunday

            # Apply quality controls only if quality_controller exists
            if self.quality_controller:
                df = self.quality_controller.validate_coordinates(df)
                df = self.quality_controller.validate_travel_times(df)
                df = self.quality_controller.handle_duplicates(df)

                self.quality_controller.metrics.records_after_quality_control = len(df)
                self.quality_controller.metrics.processing_time_seconds = (
                    time.time() - start_time
                )
                logger.info(
                    f"Data preprocessing completed: {len(df)} records after quality control"
                )
            else:
                logger.info(
                    f"Data preprocessing completed: {len(df)} records (no quality control applied)"
                )

            self.raw_data = df
            return df

        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise

    def load_zone_data(self) -> gpd.GeoDataFrame:
        """Load EMC2 zone grid shapefile."""
        try:
            logger.info(f"Loading zone data from {self.config.zone_shapefile}")

            zone_gdf = gpd.read_file(self.config.zone_shapefile)

            # Ensure Lambert 93 CRS
            if zone_gdf.crs != "EPSG:2154":
                zone_gdf = zone_gdf.to_crs("EPSG:2154")

            logger.info(f"Loaded {len(zone_gdf)} zones from EMC2 grid")
            self.zone_data = zone_gdf
            return zone_gdf

        except Exception as e:
            logger.error(f"Error loading zone data: {e}")
            raise

    def perform_spatial_joins(
        self, df: pd.DataFrame, zone_gdf: gpd.GeoDataFrame
    ) -> pd.DataFrame:
        """Perform spatial joins to assign zones to origin and destination points."""
        logger.info("Performing spatial joins for zone assignment")

        # Transform coordinates to Lambert 93
        origins = []
        destinations = []

        for _, row in df.iterrows():
            # Transform to Lambert 93
            origin_x, origin_y = self.transformer.transform(
                row["Origine X"], row["Origine Y"]
            )
            dest_x, dest_y = self.transformer.transform(
                row["Destination X"], row["Destination Y"]
            )

            origins.append(Point(origin_x, origin_y))
            destinations.append(Point(dest_x, dest_y))

        # Create GeoDataFrames for spatial joins
        origins_gdf = gpd.GeoDataFrame(df.index, geometry=origins, crs="EPSG:2154")
        destinations_gdf = gpd.GeoDataFrame(
            df.index, geometry=destinations, crs="EPSG:2154"
        )

        # Perform spatial joins
        origin_zones = gpd.sjoin(
            origins_gdf,
            zone_gdf[["NOM_DTIR", "geometry"]],
            how="left",
            predicate="within",
        )
        dest_zones = gpd.sjoin(
            destinations_gdf,
            zone_gdf[["NOM_DTIR", "geometry"]],
            how="left",
            predicate="within",
        )

        # Add zone information to dataframe
        df_with_zones = df.copy()
        df_with_zones["origin_zone"] = origin_zones["NOM_DTIR"].fillna("HorsZone")
        df_with_zones["destination_zone"] = dest_zones["NOM_DTIR"].fillna("HorsZone")

        # Calculate spatial join success rates
        total_points = len(df) * 2  # origins + destinations
        matched_origins = origin_zones["NOM_DTIR"].notna().sum()
        matched_destinations = dest_zones["NOM_DTIR"].notna().sum()
        total_matched = matched_origins + matched_destinations

        spatial_join_success_rate = (total_matched / total_points) * 100
        unmatched_origins = len(df) - matched_origins
        unmatched_destinations = len(df) - matched_destinations

        # Update metrics if quality controller exists
        if self.quality_controller:
            self.quality_controller.metrics.spatial_join_success_rate = spatial_join_success_rate
            self.quality_controller.metrics.unmatched_origins = unmatched_origins
            self.quality_controller.metrics.unmatched_destinations = unmatched_destinations

        logger.info(f"Spatial join completed: {spatial_join_success_rate:.1f}% success rate")
        logger.info(f"Unmatched origins: {unmatched_origins}")
        logger.info(f"Unmatched destinations: {unmatched_destinations}")

        self.clean_data = df_with_zones
        return df_with_zones

    def calculate_hourly_flows(
        self, df_with_zones: pd.DataFrame, zone_gdf: gpd.GeoDataFrame
    ) -> gpd.GeoDataFrame:
        """Calculate hourly flow aggregations for each zone."""
        logger.info("Calculating hourly flow aggregations")

        # Initialize result dataframe with all zones
        result_gdf = zone_gdf.copy()

        # Initialize hourly columns
        for hour in range(24):
            result_gdf[f"orig_h{hour:02d}"] = 0
            result_gdf[f"dest_h{hour:02d}"] = 0
            result_gdf[f"net_h{hour:02d}"] = 0

        # Calculate origin flows by hour
        origin_hourly = (
            df_with_zones.groupby(["origin_zone", "hour"])
            .size()
            .reset_index(name="count")
        )
        for _, row in origin_hourly.iterrows():
            zone_mask = result_gdf["NOM_DTIR"] == row["origin_zone"]
            if zone_mask.any():
                col_name = f'orig_h{int(row["hour"]):02d}'
                result_gdf.loc[zone_mask, col_name] = row["count"]

        # Calculate destination flows by hour
        dest_hourly = (
            df_with_zones.groupby(["destination_zone", "hour"])
            .size()
            .reset_index(name="count")
        )
        for _, row in dest_hourly.iterrows():
            zone_mask = result_gdf["NOM_DTIR"] == row["destination_zone"]
            if zone_mask.any():
                col_name = f'dest_h{int(row["hour"]):02d}'
                result_gdf.loc[zone_mask, col_name] = row["count"]

        # Calculate net flows
        for hour in range(24):
            orig_col = f"orig_h{hour:02d}"
            dest_col = f"dest_h{hour:02d}"
            net_col = f"net_h{hour:02d}"
            result_gdf[net_col] = result_gdf[dest_col] - result_gdf[orig_col]

        logger.info("Hourly flow calculations completed")
        return result_gdf

    def calculate_daily_flows(
        self, df_with_zones: pd.DataFrame, zone_gdf: gpd.GeoDataFrame
    ) -> gpd.GeoDataFrame:
        """Calculate daily flow aggregations for each zone (Monday-Friday)."""
        logger.info("Calculating daily flow aggregations")

        # Initialize result dataframe with all zones
        result_gdf = zone_gdf.copy()

        # Day abbreviations for column names
        day_abbrevs = ["mon", "tue", "wed", "thu", "fri"]

        # Initialize daily columns
        for day_abbrev in day_abbrevs:
            result_gdf[f"orig_{day_abbrev}"] = 0
            result_gdf[f"dest_{day_abbrev}"] = 0
            result_gdf[f"net_{day_abbrev}"] = 0

        # Filter weekdays only (0-4 = Monday-Friday)
        weekday_data = df_with_zones[df_with_zones["weekday_num"] < 5].copy()

        # Calculate origin flows by weekday
        origin_daily = (
            weekday_data.groupby(["origin_zone", "weekday_num"])
            .size()
            .reset_index(name="count")
        )
        for _, row in origin_daily.iterrows():
            zone_mask = result_gdf["NOM_DTIR"] == row["origin_zone"]
            if zone_mask.any() and row["weekday_num"] < 5:
                col_name = f'orig_{day_abbrevs[int(row["weekday_num"])]}'
                result_gdf.loc[zone_mask, col_name] = row["count"]

        # Calculate destination flows by weekday
        dest_daily = (
            weekday_data.groupby(["destination_zone", "weekday_num"])
            .size()
            .reset_index(name="count")
        )
        for _, row in dest_daily.iterrows():
            zone_mask = result_gdf["NOM_DTIR"] == row["destination_zone"]
            if zone_mask.any() and row["weekday_num"] < 5:
                col_name = f'dest_{day_abbrevs[int(row["weekday_num"])]}'
                result_gdf.loc[zone_mask, col_name] = row["count"]

        # Calculate net flows
        for day_abbrev in day_abbrevs:
            orig_col = f"orig_{day_abbrev}"
            dest_col = f"dest_{day_abbrev}"
            net_col = f"net_{day_abbrev}"
            result_gdf[net_col] = result_gdf[dest_col] - result_gdf[orig_col]

        logger.info("Daily flow calculations completed")
        return result_gdf

    def calculate_total_flows(
        self, df_with_zones: pd.DataFrame, zone_gdf: gpd.GeoDataFrame
    ) -> gpd.GeoDataFrame:
        """Calculate total flow aggregations for each zone."""
        logger.info("Calculating total flow aggregations")

        # Initialize result dataframe with all zones
        result_gdf = zone_gdf.copy()
        result_gdf["orig_cnt"] = 0
        result_gdf["dest_cnt"] = 0
        result_gdf["net_flow"] = 0

        # Calculate origin flows
        origin_totals = df_with_zones.groupby("origin_zone").size()
        for zone, count in origin_totals.items():
            zone_mask = result_gdf["NOM_DTIR"] == zone
            if zone_mask.any():
                result_gdf.loc[zone_mask, "orig_cnt"] = count

        # Calculate destination flows
        dest_totals = df_with_zones.groupby("destination_zone").size()
        for zone, count in dest_totals.items():
            zone_mask = result_gdf["NOM_DTIR"] == zone
            if zone_mask.any():
                result_gdf.loc[zone_mask, "dest_cnt"] = count

        # Calculate net flows
        result_gdf["net_flow"] = result_gdf["dest_cnt"] - result_gdf["orig_cnt"]

        logger.info("Total flow calculations completed")
        return result_gdf

    def export_shapefile(self, gdf: gpd.GeoDataFrame, temporal_level: str) -> str:
        """Export GeoDataFrame to shapefile with proper naming."""
        filename = f"{self.vehicle_type}_flows_{temporal_level}.shp"
        output_path = self.output_path / filename

        # Ensure Lambert 93 CRS
        if gdf.crs != "EPSG:2154":
            gdf = gdf.to_crs("EPSG:2154")

        # Export shapefile
        gdf.to_file(output_path, driver="ESRI Shapefile")
        logger.info(f"Shapefile exported: {output_path}")
        return str(output_path)

    def export_excel(self, gdf: gpd.GeoDataFrame, temporal_level: str) -> str:
        """Export tabular data to Excel without geometries."""
        filename = f"{self.vehicle_type}_flows_{temporal_level}.xlsx"
        output_path = self.output_path / filename

        # Remove geometry column for Excel export
        df_export = gdf.drop(columns=["geometry"])

        # Export to Excel
        df_export.to_excel(
            output_path, index=False, sheet_name=f"{temporal_level}_flows"
        )
        logger.info(f"Excel file exported: {output_path}")
        return str(output_path)

    def validate_outputs(
        self,
        hourly_gdf: gpd.GeoDataFrame,
        daily_gdf: gpd.GeoDataFrame,
        total_gdf: gpd.GeoDataFrame,
    ) -> Dict[str, bool]:
        """Validate that outputs are consistent and complete."""
        logger.info("Validating output consistency")

        validation_results = {}

        # Check that all EMC2 zones are preserved
        expected_zones = set(self.zone_data["NOM_DTIR"].unique())
        hourly_zones = set(hourly_gdf["NOM_DTIR"].unique())
        daily_zones = set(daily_gdf["NOM_DTIR"].unique())
        total_zones = set(total_gdf["NOM_DTIR"].unique())

        validation_results["all_zones_preserved"] = (
            expected_zones == hourly_zones == daily_zones == total_zones
        )

        # Check that temporal aggregations sum to total
        hourly_total_orig = sum([hourly_gdf[f"orig_h{h:02d}"].sum() for h in range(24)])
        daily_total_orig = sum(
            [
                daily_gdf[f"orig_{day}"].sum()
                for day in ["mon", "tue", "wed", "thu", "fri"]
            ]
        )
        total_orig = total_gdf["orig_cnt"].sum()

        # Allow for small differences due to weekend data in hourly vs daily
        validation_results["origin_totals_consistent"] = abs(
            hourly_total_orig - total_orig
        ) < (total_orig * 0.1)

        # Check coordinate system consistency
        validation_results["coordinate_system_consistent"] = (
            hourly_gdf.crs == daily_gdf.crs == total_gdf.crs == "EPSG:2154"
        )

        # Check for null values in key columns
        validation_results["no_null_values"] = (
            not hourly_gdf["NOM_DTIR"].isnull().any()
            and not daily_gdf["NOM_DTIR"].isnull().any()
            and not total_gdf["NOM_DTIR"].isnull().any()
        )

        # Log validation results
        for check, result in validation_results.items():
            status = "PASSED" if result else "FAILED"
            logger.info(f"Validation {check}: {status}")

        return validation_results

    def generate_summary_statistics(self) -> Dict:
        """Generate comprehensive summary statistics."""
        if self.clean_data is None:
            return {}

        summary = {
            "vehicle_type": self.vehicle_type,
            "input_file": self.input_csv_path,
            "data_characteristics": {
                "unique_vehicles": self.clean_data["ID"].nunique(),
                "date_range": f"{self.clean_data['datetime'].min()} to {self.clean_data['datetime'].max()}",
                "od_types": list(self.clean_data["OD"].unique()),
                "zones_with_origins": self.clean_data["origin_zone"].nunique(),
                "zones_with_destinations": self.clean_data[
                    "destination_zone"
                ].nunique(),
                "total_zones_in_grid": len(self.zone_data),
            },
        }

        # Add quality control metrics if available
        if self.quality_controller:
            metrics = self.quality_controller.get_metrics()
            summary["processing_metrics"] = {
                "total_input_records": metrics.total_input_records,
                "records_after_quality_control": metrics.records_after_quality_control,
                "coordinate_outliers_removed": metrics.coordinate_outliers_removed,
                "invalid_travel_times_removed": metrics.invalid_travel_times_removed,
                "duplicates_removed": metrics.duplicates_removed,
                "spatial_join_success_rate": f"{metrics.spatial_join_success_rate:.1f}%",
                "unmatched_origins": metrics.unmatched_origins,
                "unmatched_destinations": metrics.unmatched_destinations,
                "processing_time_seconds": f"{metrics.processing_time_seconds:.2f}",
            }
        else:
            summary["processing_metrics"] = {
                "total_input_records": len(self.raw_data),
                "records_after_quality_control": len(self.clean_data),
                "quality_control_applied": False,
                "coordinate_outliers_removed": 0,
                "invalid_travel_times_removed": 0,
                "duplicates_removed": 0,
            }

        return summary

    def run_complete_analysis(self) -> Dict:
        """Execute complete spatial flow analysis workflow."""
        start_time = time.time()
        logger.info(f"Starting complete spatial flow analysis for {self.vehicle_type}")

        try:
            # Step 1: Load and preprocess data
            df = self.load_and_preprocess_data()

            # Step 2: Load zone data
            zone_gdf = self.load_zone_data()

            # Step 3: Perform spatial joins
            df_with_zones = self.perform_spatial_joins(df, zone_gdf)

            # Step 4: Calculate flow aggregations
            hourly_flows = self.calculate_hourly_flows(df_with_zones, zone_gdf)
            daily_flows = self.calculate_daily_flows(df_with_zones, zone_gdf)
            total_flows = self.calculate_total_flows(df_with_zones, zone_gdf)

            # Step 5: Export results
            exported_files = {}

            # Export shapefiles
            exported_files["hourly_shp"] = self.export_shapefile(hourly_flows, "hourly")
            exported_files["daily_shp"] = self.export_shapefile(daily_flows, "daily")
            exported_files["total_shp"] = self.export_shapefile(total_flows, "total")

            # Export Excel files
            exported_files["hourly_xlsx"] = self.export_excel(hourly_flows, "hourly")
            exported_files["daily_xlsx"] = self.export_excel(daily_flows, "daily")
            exported_files["total_xlsx"] = self.export_excel(total_flows, "total")

            # Step 6: Validate outputs
            validation_results = self.validate_outputs(
                hourly_flows, daily_flows, total_flows
            )

            # Step 7: Generate summary
            summary = self.generate_summary_statistics()
            summary["exported_files"] = exported_files
            summary["validation_results"] = validation_results
            summary["total_processing_time"] = f"{time.time() - start_time:.2f} seconds"

            logger.info(
                f"Analysis completed successfully in {summary['total_processing_time']}"
            )
            return summary

        except Exception as e:
            logger.error(f"Analysis failed: {e}")
            raise

def create_comprehensive_excel_report(
    hv_summary: Dict, vul_summary: Dict, output_dir: Path
):
    """Create comprehensive Excel report with multiple worksheets."""
    logger.info("Creating comprehensive Excel report")

    report_path = output_dir / "spatial_flow_analysis_report.xlsx"

    with pd.ExcelWriter(report_path, engine="openpyxl") as writer:
        # Summary worksheet
        summary_data = []
        for vehicle_type, summary in [
            ("Heavy Vehicles", hv_summary),
            ("Utility Vehicles", vul_summary),
        ]:
            if summary:
                metrics = summary["processing_metrics"]
                characteristics = summary["data_characteristics"]

                summary_data.append(
                    {
                        "Vehicle Type": vehicle_type,
                        "Input Records": metrics["total_input_records"],
                        "Records After QC": metrics["records_after_quality_control"],
                        "Unique Vehicles": characteristics["unique_vehicles"],
                        "Spatial Join Success": metrics["spatial_join_success_rate"],
                        "Processing Time": metrics["processing_time_seconds"],
                        "Date Range": characteristics["date_range"],
                    }
                )

        summary_df = pd.DataFrame(summary_data)
        summary_df.to_excel(writer, sheet_name="Analysis_Summary", index=False)

        # Validation results
        validation_data = []
        for vehicle_type, summary in [
            ("Heavy Vehicles", hv_summary),
            ("Utility Vehicles", vul_summary),
        ]:
            if summary and "validation_results" in summary:
                for check, result in summary["validation_results"].items():
                    validation_data.append(
                        {
                            "Vehicle Type": vehicle_type,
                            "Validation Check": check.replace("_", " ").title(),
                            "Result": "PASSED" if result else "FAILED",
                        }
                    )

        if validation_data:
            validation_df = pd.DataFrame(validation_data)
            validation_df.to_excel(writer, sheet_name="Validation_Results", index=False)

    logger.info(f"Comprehensive report saved to: {report_path}")
    return str(report_path)


def main():
    """Main execution workflow for processing both vehicle types."""
    print("=" * 80)
    print("SPATIAL FLOW ANALYSIS SYSTEM - STRASBOURG EMC2 ZONE GRID")
    print("=" * 80)

    # Initialize configuration
    config = DataConfiguration()
    quality_config = QualityControlConfig()

    # Create output directory
    output_dir = Path(config.output_dir)
    output_dir.mkdir(exist_ok=True)

    print(f"\nConfiguration:")
    print(f"Work Directory: {config.work_dir}")
    print(f"Zone Shapefile: {config.zone_shapefile}")
    print(f"Output Directory: {config.output_dir}")
    print(
        f"Quality Controls: Coordinates={quality_config.validate_coordinates}, "
        f"Travel Times={quality_config.validate_travel_times}, "
        f"Duplicates={quality_config.handle_duplicates}"
    )

    # Define vehicle datasets
    vehicle_datasets = [
        ("hv", f"{config.work_dir}\\od_pl.csv", "Heavy Vehicles"),
        ("vul", f"{config.work_dir}\\od_vul.csv", "Utility Vehicles"),
    ]

    results = {}
    total_start_time = time.time()

    # Process each vehicle type
    for vehicle_code, csv_path, vehicle_name in vehicle_datasets:
        print(f"\n{'-' * 60}")
        print(f"PROCESSING {vehicle_name.upper()}")
        print(f"{'-' * 60}")

        try:
            # Check if input file exists
            if not Path(csv_path).exists():
                logger.warning(f"Input file not found: {csv_path}")
                print(f"⚠️  Skipping {vehicle_name} - file not found")
                continue

            # Initialize processor
            processor = SpatialFlowProcessor(
                vehicle_type=vehicle_code,
                input_csv_path=csv_path,
                config=config,
                quality_config=quality_config,
            )

            # Run analysis
            summary = processor.run_complete_analysis()
            results[vehicle_code] = summary

            # Print summary statistics
            print(f"\n✅ {vehicle_name} Analysis Completed Successfully!")
            print(f"📊 Processing Summary:")
            metrics = summary["processing_metrics"]
            print(f"   • Input Records: {metrics['total_input_records']:,}")
            print(
                f"   • Records After QC: {metrics['records_after_quality_control']:,}"
            )
            print(f"   • Spatial Join Success: {metrics['spatial_join_success_rate']}")
            print(f"   • Processing Time: {metrics['processing_time_seconds']} seconds")

            # Print exported files
            print(f"\n📁 Generated Files:")
            for file_type, file_path in summary["exported_files"].items():
                print(f"   • {file_type}: {Path(file_path).name}")

            # Print validation results
            print(f"\n✓ Validation Results:")
            for check, result in summary["validation_results"].items():
                status = "✅ PASSED" if result else "❌ FAILED"
                print(f"   • {check.replace('_', ' ').title()}: {status}")

        except Exception as e:
            logger.error(f"Failed to process {vehicle_name}: {e}")
            print(f"❌ {vehicle_name} processing failed: {e}")
            results[vehicle_code] = None

    # Generate comprehensive report
    print(f"\n{'-' * 60}")
    print("GENERATING COMPREHENSIVE REPORT")
    print(f"{'-' * 60}")

    try:
        report_path = create_comprehensive_excel_report(
            results.get("hv"), results.get("vul"), output_dir
        )
        print(f"📋 Comprehensive report saved: {Path(report_path).name}")
    except Exception as e:
        logger.error(f"Failed to create comprehensive report: {e}")
        print(f"❌ Report generation failed: {e}")

    # Final summary
    total_time = time.time() - total_start_time
    print(f"\n{'=' * 80}")
    print("ANALYSIS COMPLETE")
    print(f"{'=' * 80}")
    print(f"Total Processing Time: {total_time:.2f} seconds")
    print(f"Output Directory: {output_dir.absolute()}")

    successful_analyses = sum(1 for result in results.values() if result is not None)
    print(f"Successful Analyses: {successful_analyses}/{len(vehicle_datasets)}")

    if successful_analyses > 0:
        print(f"\n📊 Generated Outputs:")
        print(f"   • {successful_analyses * 3} Shapefiles (hourly, daily, total)")
        print(f"   • {successful_analyses * 3} Excel files (hourly, daily, total)")
        print(f"   • 1 Comprehensive analysis report")
        print(f"   • 1 Processing log file")

    print(f"\n🎯 All outputs use Lambert 93 (EPSG:2154) coordinate system")
    print(f"📍 Zone assignments based on EMC2 Nord-Alsace 2024 grid")
    print(f"📝 Check 'spatial_flow_analysis.log' for detailed processing information")


if __name__ == "__main__":
    main()
