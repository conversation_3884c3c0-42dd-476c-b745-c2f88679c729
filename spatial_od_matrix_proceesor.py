import pandas as pd
import geopandas as gpd
from pathlib import Path
from pyproj import Transformer
from shapely.geometry import Point
import warnings

warnings.filterwarnings("ignore")

# Configuration Constants
WORK_DIR = r"C:\Users\<USER>\Documents\affaires\23I711.3 Strasbourg OD PL FCD"
ZONE_SHAPEFILE = f"{WORK_DIR}\\SIG_EMC2\\EMC2_NordAlsace_2024_DTIR_31072024.shp"
OUTPUT_DIR = rf"{WORK_DIR}\\spatial_analysis_output"
COORDINATE_BOUNDS = (7.6, 7.9, 48.4, 48.7)  # min_lon, max_lon, min_lat, max_lat
MAX_TRAVEL_TIME_HOURS = 6.0

# OD Categories to process
OD_CATEGORIES = [
    "RN4_Ouest_Est",
    "RN4_Est_Ouest",
    "ROCHELLE_Sud_Nord",
    "ROCHELLE_Nord_Sud",
]


def load_od_data(csv_path: str) -> pd.DataFrame:
    """Load and parse OD data from CSV file."""
    try:
        df = pd.read_csv(csv_path, sep=";", encoding="utf-8")

        # Parse datetime columns
        df["datetime"] = pd.to_datetime(
            df["Date"] + " " + df["Heure"], format="%d/%m/%Y %H:%M"
        )
        df["origin_datetime"] = pd.to_datetime(df["Horodate origine"])
        df["destination_datetime"] = pd.to_datetime(df["Horodate destination"])

        # Calculate travel time
        df["travel_time_minutes"] = (
            df["destination_datetime"] - df["origin_datetime"]
        ).dt.total_seconds() / 60

        # Add temporal features
        df["hour"] = df["datetime"].dt.hour
        df["weekday_num"] = df["datetime"].dt.weekday  # 0=Monday, 6=Sunday

        return df
    except Exception as e:
        print(f"Error loading data from {csv_path}: {e}")
        raise


def load_zone_data() -> gpd.GeoDataFrame:
    """Load EMC2 zone grid shapefile."""
    try:
        zone_gdf = gpd.read_file(ZONE_SHAPEFILE)

        # Ensure Lambert 93 CRS
        if zone_gdf.crs != "EPSG:2154":
            zone_gdf = zone_gdf.to_crs("EPSG:2154")

        return zone_gdf
    except Exception as e:
        print(f"Error loading zone data: {e}")
        raise


def validate_data(df: pd.DataFrame, verify_checks: bool = True) -> pd.DataFrame:
    """Apply data validation checks if enabled."""
    if not verify_checks:
        return df

    initial_count = len(df)

    # Validate coordinates
    """min_lon, max_lon, min_lat, max_lat = COORDINATE_BOUNDS
    df = df[
        (df["Origine X"] >= min_lon)
        & (df["Origine X"] <= max_lon)
        & (df["Origine Y"] >= min_lat)
        & (df["Origine Y"] <= max_lat)
        & (df["Destination X"] >= min_lon)
        & (df["Destination X"] <= max_lon)
        & (df["Destination Y"] >= min_lat)
        & (df["Destination Y"] <= max_lat)
    ]

    # Validate travel times
    max_minutes = MAX_TRAVEL_TIME_HOURS * 60
    df = df[
        (df["travel_time_minutes"] > 0) & (df["travel_time_minutes"] <= max_minutes)
    ]"""

    # Remove duplicates
    df = df.drop_duplicates(subset=["ID", "datetime"], keep="first")

    removed_count = initial_count - len(df)
    if removed_count > 0:
        print(f"  Validation: Removed {removed_count} invalid records")

    return df


def perform_spatial_joins(df: pd.DataFrame, zone_gdf: gpd.GeoDataFrame) -> pd.DataFrame:
    """Perform spatial joins to assign zones to origin and destination points."""
    transformer = Transformer.from_crs("EPSG:4326", "EPSG:2154", always_xy=True)

    # Transform coordinates to Lambert 93
    origins = []
    destinations = []

    for _, row in df.iterrows():
        origin_x, origin_y = transformer.transform(row["Origine X"], row["Origine Y"])
        dest_x, dest_y = transformer.transform(
            row["Destination X"], row["Destination Y"]
        )

        origins.append(Point(origin_x, origin_y))
        destinations.append(Point(dest_x, dest_y))

    # Create GeoDataFrames for spatial joins
    origins_gdf = gpd.GeoDataFrame(df.index, geometry=origins, crs="EPSG:2154")
    destinations_gdf = gpd.GeoDataFrame(
        df.index, geometry=destinations, crs="EPSG:2154"
    )

    # Perform spatial joins
    zone_columns = ["NOM_DTIR", "NOM_D10", "NOM_D30", "geometry"]
    origin_zones = gpd.sjoin(
        origins_gdf, zone_gdf[zone_columns], how="left", predicate="within"
    )
    dest_zones = gpd.sjoin(
        destinations_gdf,
        zone_gdf[zone_columns],
        how="left",
        predicate="within",
    )

    # Add zone information to dataframe
    df_with_zones = df.copy()
    df_with_zones["origin_NOM_DTIR"] = origin_zones["NOM_DTIR"].fillna("HorsZone")
    df_with_zones["destination_NOM_DTIR"] = dest_zones["NOM_DTIR"].fillna("HorsZone")
    df_with_zones["origin_NOM_D10"] = origin_zones["NOM_D10"].fillna("HorsZone")
    df_with_zones["destination_NOM_D10"] = dest_zones["NOM_D10"].fillna("HorsZone")
    df_with_zones["origin_NOM_D30"] = origin_zones["NOM_D30"].fillna("HorsZone")
    df_with_zones["destination_NOM_D30"] = dest_zones["NOM_D30"].fillna("HorsZone")

    return df_with_zones


def create_od_matrix_by_category(
    df_with_zones: pd.DataFrame, od_category: str
) -> pd.DataFrame:
    """Create OD matrix for a specific directional category."""
    # Filter data for the specific OD category
    category_data = df_with_zones[df_with_zones["OD"] == od_category].copy()

    if len(category_data) == 0:
        print(f"  Warning: No data found for category {od_category}")
        return pd.DataFrame()

    # Group by origin-destination pairs and count flows
    od_matrix = (
        category_data.groupby(
            [
                "origin_NOM_DTIR",
                "destination_NOM_DTIR",
                "origin_NOM_D10",
                "destination_NOM_D10",
                "origin_NOM_D30",
                "destination_NOM_D30",
            ]
        )
        .size()
        .reset_index(name="count")
    )

    # Add the OD category column
    od_matrix["OD_category"] = od_category

    # Reorder columns for clarity
    od_matrix = od_matrix[
        [
            "origin_NOM_DTIR",
            "destination_NOM_DTIR",
            "origin_NOM_D10",
            "destination_NOM_D10",
            "origin_NOM_D30",
            "destination_NOM_D30",
            "count",
            "OD_category",
        ]
    ]

    return od_matrix


def create_hourly_od_matrix_by_category(
    df_with_zones: pd.DataFrame, od_category: str
) -> pd.DataFrame:
    """Create hourly OD matrix for a specific directional category."""
    # Filter data for the specific OD category
    category_data = df_with_zones[df_with_zones["OD"] == od_category].copy()

    if len(category_data) == 0:
        print(f"  Warning: No data found for category {od_category}")
        return pd.DataFrame()

    # Group by origin-destination pairs and hour, then count flows
    hourly_od_matrix = (
        category_data.groupby(
            [
                "origin_NOM_DTIR",
                "destination_NOM_DTIR",
                "origin_NOM_D10",
                "destination_NOM_D10",
                "origin_NOM_D30",
                "destination_NOM_D30",
                "hour",
            ]
        )
        .size()
        .reset_index(name="count")
    )

    # Add the OD category column
    hourly_od_matrix["OD_category"] = od_category

    # Reorder columns for clarity
    hourly_od_matrix = hourly_od_matrix[
        [
            "origin_NOM_DTIR",
            "destination_NOM_DTIR",
            "origin_NOM_D10",
            "destination_NOM_D10",
            "origin_NOM_D30",
            "destination_NOM_D30",
            "hour",
            "count",
            "OD_category",
        ]
    ]

    return hourly_od_matrix


def create_daily_od_matrix_by_category(
    df_with_zones: pd.DataFrame, od_category: str
) -> pd.DataFrame:
    """Create daily OD matrix for a specific directional category (Monday-Friday)."""
    # Filter data for the specific OD category and weekdays only
    category_data = df_with_zones[
        (df_with_zones["OD"] == od_category) & (df_with_zones["weekday_num"] < 5)
    ].copy()

    if len(category_data) == 0:
        print(f"  Warning: No data found for category {od_category}")
        return pd.DataFrame()

    # Group by origin-destination pairs and weekday, then count flows
    daily_od_matrix = (
        category_data.groupby(
            [
                "origin_NOM_DTIR",
                "destination_NOM_DTIR",
                "origin_NOM_D10",
                "destination_NOM_D10",
                "origin_NOM_D30",
                "destination_NOM_D30",
                "weekday_num",
            ]
        )
        .size()
        .reset_index(name="count")
    )

    # Map weekday numbers to names
    day_names = {0: "Monday", 1: "Tuesday", 2: "Wednesday", 3: "Thursday", 4: "Friday"}
    daily_od_matrix["weekday"] = daily_od_matrix["weekday_num"].map(day_names)

    # Add the OD category column
    daily_od_matrix["OD_category"] = od_category

    # Reorder columns for clarity
    daily_od_matrix = daily_od_matrix[
        [
            "origin_NOM_DTIR",
            "destination_NOM_DTIR",
            "origin_NOM_D10",
            "destination_NOM_D10",
            "origin_NOM_D30",
            "destination_NOM_D30",
            "weekday",
            "count",
            "OD_category",
        ]
    ]

    return daily_od_matrix


def export_od_matrix_csv(
    od_matrix: pd.DataFrame,
    vehicle_type: str,
    temporal_level: str,
    od_category: str,
    output_dir: Path,
) -> str:
    """Export OD matrix to CSV file."""
    # Clean category name for filename
    category_clean = od_category.replace(" ", "_").replace("/", "_")
    filename = f"{vehicle_type}_od_matrix_{temporal_level}_{category_clean}.csv"
    output_path = output_dir / filename

    od_matrix.to_csv(output_path, index=False)
    return str(output_path)


def export_od_matrix_excel(
    od_matrices: dict, vehicle_type: str, temporal_level: str, output_dir: Path
) -> str:
    """Export all OD matrices for a temporal level to a single Excel file with multiple sheets."""
    filename = f"{vehicle_type}_od_matrices_{temporal_level}.xlsx"
    output_path = output_dir / filename

    with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
        for od_category, od_matrix in od_matrices.items():
            if not od_matrix.empty:
                # Clean sheet name (Excel sheet names have character limits)
                sheet_name = od_category.replace("PASSES IN ", "").replace("/", "_")[
                    :31
                ]
                od_matrix.to_excel(writer, sheet_name=sheet_name, index=False)

    return str(output_path)


def create_summary_statistics(od_matrices: dict, od_category: str) -> dict:
    """Create summary statistics for OD matrices."""
    stats = {}

    if od_category in od_matrices and not od_matrices[od_category].empty:
        od_matrix = od_matrices[od_category]

        stats[od_category] = {
            "total_flows": od_matrix["count"].sum(),
            "unique_od_pairs": len(od_matrix),
            "unique_origins_DTIR": od_matrix["origin_NOM_DTIR"].nunique(),
            "unique_destinations_DTIR": od_matrix["destination_NOM_DTIR"].nunique(),
            "unique_origins_D10": od_matrix["origin_NOM_D10"].nunique(),
            "unique_destinations_D10": od_matrix["destination_NOM_D10"].nunique(),
            "unique_origins_D30": od_matrix["origin_NOM_D30"].nunique(),
            "unique_destinations_D30": od_matrix["destination_NOM_D30"].nunique(),
            "max_flow_count": od_matrix["count"].max(),
            "avg_flow_count": od_matrix["count"].mean(),
            "top_od_pair": od_matrix.loc[
                od_matrix["count"].idxmax(),
                ["origin_NOM_DTIR", "destination_NOM_DTIR", "count"],
            ].to_dict(),
        }

    return stats


def process_vehicle_type(
    vehicle_type: str,
    csv_path: str,
    zone_gdf: gpd.GeoDataFrame,
    output_dir: Path,
    verify_checks: bool = True,
) -> bool:
    """Process a single vehicle type through the complete OD matrix workflow."""
    try:
        print(f"\nProcessing {vehicle_type}...")

        # Check if input file exists
        if not Path(csv_path).exists():
            print(f"  Input file not found: {csv_path}")
            return False

        # Load and validate data
        df = load_od_data(csv_path)
        print(f"  Loaded {len(df):,} records")

        df = validate_data(df, verify_checks)
        print(f"  After validation: {len(df):,} records")

        # Check available OD categories in the data
        available_categories = df["OD"].unique() if "OD" in df.columns else []
        print(f"  Available OD categories: {list(available_categories)}")

        # Perform spatial joins
        df_with_zones = perform_spatial_joins(df, zone_gdf)

        # Process each temporal level
        for temporal_level in ["total", "hourly", "daily"]:
            od_matrices = {}

            for od_category in OD_CATEGORIES:
                if od_category in available_categories:
                    if temporal_level == "total":
                        od_matrix = create_od_matrix_by_category(
                            df_with_zones, od_category
                        )
                    elif temporal_level == "hourly":
                        od_matrix = create_hourly_od_matrix_by_category(
                            df_with_zones, od_category
                        )
                    elif temporal_level == "daily":
                        od_matrix = create_daily_od_matrix_by_category(
                            df_with_zones, od_category
                        )

                    if not od_matrix.empty:
                        od_matrices[od_category] = od_matrix

                        # Export individual CSV for each category
                        export_od_matrix_csv(
                            od_matrix,
                            vehicle_type,
                            temporal_level,
                            od_category,
                            output_dir,
                        )

                        print(
                            f"    {od_category}: {len(od_matrix)} OD pairs, {od_matrix['count'].sum()} total flows"
                        )

            # Export combined Excel file for the temporal level
            if od_matrices:
                export_od_matrix_excel(
                    od_matrices, vehicle_type, temporal_level, output_dir
                )

        print(f"  ✓ {vehicle_type} OD matrices completed successfully")
        return True

    except Exception as e:
        print(f"  ✗ {vehicle_type} failed: {e}")
        return False


def create_comprehensive_summary(results: dict, output_dir: Path):
    """Create comprehensive summary report of OD matrix analysis."""
    summary_path = output_dir / "od_matrix_analysis_summary.txt"

    with open(summary_path, "w") as f:
        f.write("OD MATRIX ANALYSIS SUMMARY - STRASBOURG EMC2 ZONE GRID\n")
        f.write("=" * 60 + "\n\n")

        for vehicle_type, success in results.items():
            if success:
                f.write(f"{vehicle_type.upper()} PROCESSING: SUCCESS\n")
                f.write("-" * 40 + "\n")

                # Count generated files
                csv_files = list(output_dir.glob(f"{vehicle_type}_od_matrix_*.csv"))
                excel_files = list(
                    output_dir.glob(f"{vehicle_type}_od_matrices_*.xlsx")
                )

                f.write(f"Generated CSV files: {len(csv_files)}\n")
                f.write(f"Generated Excel files: {len(excel_files)}\n")

                f.write("\nGenerated Files:\n")
                for file_path in sorted(csv_files + excel_files):
                    f.write(f"  • {file_path.name}\n")
                f.write("\n")
            else:
                f.write(f"{vehicle_type.upper()} PROCESSING: FAILED\n")
                f.write("-" * 40 + "\n\n")

        f.write("OD MATRIX STRUCTURE:\n")
        f.write("-" * 20 + "\n")
        f.write("Each OD matrix contains:\n")
        f.write("  • origin_NOM_DTIR: Origin zone name (DTIR level)\n")
        f.write("  • destination_NOM_DTIR: Destination zone name (DTIR level)\n")
        f.write("  • origin_NOM_D10: Origin zone name (D10 level)\n")
        f.write("  • destination_NOM_D10: Destination zone name (D10 level)\n")
        f.write("  • origin_NOM_D30: Origin zone name (D30 level)\n")
        f.write("  • destination_NOM_D30: Destination zone name (D30 level)\n")
        f.write("  • count: Number of trips for this OD pair\n")
        f.write("  • OD_category: Directional category\n")
        f.write("  • hour/weekday: Temporal dimension (for hourly/daily matrices)\n")
        f.write("\n")

        f.write("DIRECTIONAL CATEGORIES:\n")
        f.write("-" * 22 + "\n")
        for category in OD_CATEGORIES:
            f.write(f"  • {category}\n")

    print(f"Summary report saved: {summary_path.name}")


def main():
    """Main execution workflow for processing both vehicle types."""
    print("OD Matrix Analysis - Strasbourg EMC2 Zone Grid")
    print("=" * 50)

    # Create output directory
    output_dir = Path(OUTPUT_DIR)
    output_dir.mkdir(exist_ok=True)

    # Load zone data
    print("Loading EMC2 zone grid...")
    zone_gdf = load_zone_data()
    print(f"Loaded {len(zone_gdf)} zones")

    # Define vehicle datasets
    vehicle_datasets = [
        ("hv", f"{WORK_DIR}\\od_pl.csv", "Heavy Vehicles"),
        ("vul", f"{WORK_DIR}\\od_vul.csv", "Utility Vehicles"),
    ]

    # Process each vehicle type
    results = {}
    for vehicle_code, csv_path, vehicle_name in vehicle_datasets:
        results[vehicle_code] = process_vehicle_type(
            vehicle_code, csv_path, zone_gdf, output_dir, verify_checks=True
        )

    # Create comprehensive summary
    create_comprehensive_summary(results, output_dir)

    # Final summary
    successful_count = sum(1 for success in results.values() if success)
    print(f"\n" + "=" * 50)
    print(
        f"OD Matrix Analysis complete: {successful_count}/{len(vehicle_datasets)} vehicle types processed"
    )
    print(f"Output directory: {output_dir.absolute()}")

    if successful_count > 0:
        total_files = len(list(output_dir.glob("*od_matrix*.csv"))) + len(
            list(output_dir.glob("*od_matrices*.xlsx"))
        )
        print(f"Generated {total_files} OD matrix files")
        print(f"File types: Individual CSV matrices + Combined Excel workbooks")


if __name__ == "__main__":
    main()
