"""
Simplified Spatial Flow Analysis for Heavy Vehicle and Utility Vehicle OD Data
Strasbourg - EMC2 Zone Grid Analysis

Functional implementation focusing on core spatial flow analysis capabilities.
"""

import pandas as pd
import geopandas as gpd
from pathlib import Path
from pyproj import Transformer
from shapely.geometry import Point
import warnings

warnings.filterwarnings("ignore")

# Configuration Constants
WORK_DIR = r"C:\Users\<USER>\Documents\affaires\23I711.3 Strasbourg OD PL FCD"
ZONE_SHAPEFILE = f"{WORK_DIR}\\SIG_EMC2\\EMC2_NordAlsace_2024_DTIR_31072024.shp"
OUTPUT_DIR = fr"{WORK_DIR}\\spatial_analysis_output"
COORDINATE_BOUNDS = (7.6, 7.9, 48.4, 48.7)  # min_lon, max_lon, min_lat, max_lat
MAX_TRAVEL_TIME_HOURS = 6.0


def load_od_data(csv_path: str) -> pd.DataFrame:
    """Load and parse OD data from CSV file."""
    try:
        df = pd.read_csv(csv_path, sep=";", encoding="utf-8")

        # Parse datetime columns
        df["datetime"] = pd.to_datetime(
            df["Date"] + " " + df["Heure"], format="%d/%m/%Y %H:%M"
        )
        df["origin_datetime"] = pd.to_datetime(df["Horodate origine"])
        df["destination_datetime"] = pd.to_datetime(df["Horodate destination"])

        # Calculate travel time
        df["travel_time_minutes"] = (
            df["destination_datetime"] - df["origin_datetime"]
        ).dt.total_seconds() / 60

        # Add temporal features
        df["hour"] = df["datetime"].dt.hour
        df["weekday_num"] = df["datetime"].dt.weekday  # 0=Monday, 6=Sunday

        return df
    except Exception as e:
        print(f"Error loading data from {csv_path}: {e}")
        raise


def load_zone_data() -> gpd.GeoDataFrame:
    """Load EMC2 zone grid shapefile."""
    try:
        zone_gdf = gpd.read_file(ZONE_SHAPEFILE)

        # Ensure Lambert 93 CRS
        if zone_gdf.crs != "EPSG:2154":
            zone_gdf = zone_gdf.to_crs("EPSG:2154")

        return zone_gdf
    except Exception as e:
        print(f"Error loading zone data: {e}")
        raise


def validate_data(df: pd.DataFrame, verify_checks: bool = True) -> pd.DataFrame:
    """Apply data validation checks if enabled."""
    if not verify_checks:
        return df

    initial_count = len(df)

    # Validate coordinates
    """min_lon, max_lon, min_lat, max_lat = COORDINATE_BOUNDS
    df = df[
        (df["Origine X"] >= min_lon)
        & (df["Origine X"] <= max_lon)
        & (df["Origine Y"] >= min_lat)
        & (df["Origine Y"] <= max_lat)
        & (df["Destination X"] >= min_lon)
        & (df["Destination X"] <= max_lon)
        & (df["Destination Y"] >= min_lat)
        & (df["Destination Y"] <= max_lat)
    ]

    # Validate travel times
    max_minutes = MAX_TRAVEL_TIME_HOURS * 60
    df = df[
        (df["travel_time_minutes"] > 0) & (df["travel_time_minutes"] <= max_minutes)
    ]"""

    # Remove duplicates
    df = df.drop_duplicates(subset=["ID", "datetime"], keep="first")

    removed_count = initial_count - len(df)
    if removed_count > 0:
        print(f"  Validation: Removed {removed_count} invalid records")

    return df


def perform_spatial_joins(df: pd.DataFrame, zone_gdf: gpd.GeoDataFrame) -> pd.DataFrame:
    """Perform spatial joins to assign zones to origin and destination points."""
    transformer = Transformer.from_crs("EPSG:4326", "EPSG:2154", always_xy=True)

    # Transform coordinates to Lambert 93
    origins = []
    destinations = []

    for _, row in df.iterrows():
        origin_x, origin_y = transformer.transform(row["Origine X"], row["Origine Y"])
        dest_x, dest_y = transformer.transform(
            row["Destination X"], row["Destination Y"]
        )

        origins.append(Point(origin_x, origin_y))
        destinations.append(Point(dest_x, dest_y))

    # Create GeoDataFrames for spatial joins
    origins_gdf = gpd.GeoDataFrame(df.index, geometry=origins, crs="EPSG:2154")
    destinations_gdf = gpd.GeoDataFrame(
        df.index, geometry=destinations, crs="EPSG:2154"
    )

    # Perform spatial joins
    origin_zones = gpd.sjoin(
        origins_gdf, zone_gdf[["NOM_DTIR", "geometry"]], how="left", predicate="within"
    )
    dest_zones = gpd.sjoin(
        destinations_gdf,
        zone_gdf[["NOM_DTIR", "geometry"]],
        how="left",
        predicate="within",
    )

    # Add zone information to dataframe
    df_with_zones = df.copy()
    df_with_zones["origin_zone"] = origin_zones["NOM_DTIR"].fillna("HorsZone")
    df_with_zones["destination_zone"] = dest_zones["NOM_DTIR"].fillna("HorsZone")

    return df_with_zones


def calculate_hourly_flows(
    df_with_zones: pd.DataFrame, zone_gdf: gpd.GeoDataFrame
) -> gpd.GeoDataFrame:
    """Calculate hourly flow aggregations for each zone."""
    result_gdf = zone_gdf.copy()

    # Initialize hourly columns
    for hour in range(24):
        result_gdf[f"orig_h{hour:02d}"] = 0
        result_gdf[f"dest_h{hour:02d}"] = 0
        result_gdf[f"net_h{hour:02d}"] = 0

    # Calculate origin flows by hour
    origin_hourly = (
        df_with_zones.groupby(["origin_zone", "hour"]).size().reset_index(name="count")
    )
    for _, row in origin_hourly.iterrows():
        zone_mask = result_gdf["NOM_DTIR"] == row["origin_zone"]
        if zone_mask.any():
            col_name = f'orig_h{int(row["hour"]):02d}'
            result_gdf.loc[zone_mask, col_name] = row["count"]

    # Calculate destination flows by hour
    dest_hourly = (
        df_with_zones.groupby(["destination_zone", "hour"])
        .size()
        .reset_index(name="count")
    )
    for _, row in dest_hourly.iterrows():
        zone_mask = result_gdf["NOM_DTIR"] == row["destination_zone"]
        if zone_mask.any():
            col_name = f'dest_h{int(row["hour"]):02d}'
            result_gdf.loc[zone_mask, col_name] = row["count"]

    # Calculate net flows
    for hour in range(24):
        orig_col = f"orig_h{hour:02d}"
        dest_col = f"dest_h{hour:02d}"
        net_col = f"net_h{hour:02d}"
        result_gdf[net_col] = result_gdf[dest_col] - result_gdf[orig_col]

    return result_gdf


def calculate_daily_flows(
    df_with_zones: pd.DataFrame, zone_gdf: gpd.GeoDataFrame
) -> gpd.GeoDataFrame:
    """Calculate daily flow aggregations for each zone (Monday-Friday)."""
    result_gdf = zone_gdf.copy()
    day_abbrevs = ["mon", "tue", "wed", "thu", "fri"]

    # Initialize daily columns
    for day_abbrev in day_abbrevs:
        result_gdf[f"orig_{day_abbrev}"] = 0
        result_gdf[f"dest_{day_abbrev}"] = 0
        result_gdf[f"net_{day_abbrev}"] = 0

    # Filter weekdays only (0-4 = Monday-Friday)
    weekday_data = df_with_zones[df_with_zones["weekday_num"] < 5].copy()

    # Calculate origin flows by weekday
    origin_daily = (
        weekday_data.groupby(["origin_zone", "weekday_num"])
        .size()
        .reset_index(name="count")
    )
    for _, row in origin_daily.iterrows():
        zone_mask = result_gdf["NOM_DTIR"] == row["origin_zone"]
        if zone_mask.any() and row["weekday_num"] < 5:
            col_name = f'orig_{day_abbrevs[int(row["weekday_num"])]}'
            result_gdf.loc[zone_mask, col_name] = row["count"]

    # Calculate destination flows by weekday
    dest_daily = (
        weekday_data.groupby(["destination_zone", "weekday_num"])
        .size()
        .reset_index(name="count")
    )
    for _, row in dest_daily.iterrows():
        zone_mask = result_gdf["NOM_DTIR"] == row["destination_zone"]
        if zone_mask.any() and row["weekday_num"] < 5:
            col_name = f'dest_{day_abbrevs[int(row["weekday_num"])]}'
            result_gdf.loc[zone_mask, col_name] = row["count"]

    # Calculate net flows
    for day_abbrev in day_abbrevs:
        orig_col = f"orig_{day_abbrev}"
        dest_col = f"dest_{day_abbrev}"
        net_col = f"net_{day_abbrev}"
        result_gdf[net_col] = result_gdf[dest_col] - result_gdf[orig_col]

    return result_gdf


def calculate_total_flows(
    df_with_zones: pd.DataFrame, zone_gdf: gpd.GeoDataFrame
) -> gpd.GeoDataFrame:
    """Calculate total flow aggregations for each zone."""
    result_gdf = zone_gdf.copy()
    result_gdf["orig_cnt"] = 0
    result_gdf["dest_cnt"] = 0
    result_gdf["net_flow"] = 0

    # Calculate origin flows
    origin_totals = df_with_zones.groupby("origin_zone").size()
    for zone, count in origin_totals.items():
        zone_mask = result_gdf["NOM_DTIR"] == zone
        if zone_mask.any():
            result_gdf.loc[zone_mask, "orig_cnt"] = count

    # Calculate destination flows
    dest_totals = df_with_zones.groupby("destination_zone").size()
    for zone, count in dest_totals.items():
        zone_mask = result_gdf["NOM_DTIR"] == zone
        if zone_mask.any():
            result_gdf.loc[zone_mask, "dest_cnt"] = count

    # Calculate net flows
    result_gdf["net_flow"] = result_gdf["dest_cnt"] - result_gdf["orig_cnt"]

    return result_gdf


def export_shapefile(
    gdf: gpd.GeoDataFrame, vehicle_type: str, temporal_level: str, output_dir: Path
) -> str:
    """Export GeoDataFrame to shapefile."""
    filename = f"{vehicle_type}_flows_{temporal_level}.shp"
    output_path = output_dir / filename

    # Ensure Lambert 93 CRS
    if gdf.crs != "EPSG:2154":
        gdf = gdf.to_crs("EPSG:2154")

    gdf.to_file(output_path, driver="ESRI Shapefile")
    return str(output_path)


def export_excel(
    gdf: gpd.GeoDataFrame, vehicle_type: str, temporal_level: str, output_dir: Path
) -> str:
    """Export tabular data to Excel without geometries."""
    filename = f"{vehicle_type}_flows_{temporal_level}.xlsx"
    output_path = output_dir / filename

    # Remove geometry column for Excel export
    df_export = gdf.drop(columns=["geometry"])
    df_export.to_excel(output_path, index=False, sheet_name=f"{temporal_level}_flows")

    return str(output_path)


def process_vehicle_type(
    vehicle_type: str,
    csv_path: str,
    zone_gdf: gpd.GeoDataFrame,
    output_dir: Path,
    verify_checks: bool = True,
) -> bool:
    """Process a single vehicle type through the complete workflow."""
    try:
        print(f"\nProcessing {vehicle_type}...")

        # Check if input file exists
        if not Path(csv_path).exists():
            print(f"  Input file not found: {csv_path}")
            return False

        # Load and validate data
        df = load_od_data(csv_path)
        print(f"  Loaded {len(df):,} records")

        df = validate_data(df, verify_checks)
        print(f"  After validation: {len(df):,} records")

        # Perform spatial joins
        df_with_zones = perform_spatial_joins(df, zone_gdf)

        # Calculate flow aggregations
        hourly_flows = calculate_hourly_flows(df_with_zones, zone_gdf)
        daily_flows = calculate_daily_flows(df_with_zones, zone_gdf)
        total_flows = calculate_total_flows(df_with_zones, zone_gdf)

        # Export results
        export_shapefile(hourly_flows, vehicle_type, "hourly", output_dir)
        export_shapefile(daily_flows, vehicle_type, "daily", output_dir)
        export_shapefile(total_flows, vehicle_type, "total", output_dir)

        export_excel(hourly_flows, vehicle_type, "hourly", output_dir)
        export_excel(daily_flows, vehicle_type, "daily", output_dir)
        export_excel(total_flows, vehicle_type, "total", output_dir)

        print(f"  ✓ {vehicle_type} completed successfully")
        return True

    except Exception as e:
        print(f"  ✗ {vehicle_type} failed: {e}")
        return False


def main():
    """Main execution workflow for processing both vehicle types."""
    print("Spatial Flow Analysis - Strasbourg EMC2 Zone Grid")
    print("=" * 50)

    # Create output directory
    output_dir = Path(OUTPUT_DIR)
    output_dir.mkdir(exist_ok=True)

    # Load zone data
    print("Loading EMC2 zone grid...")
    zone_gdf = load_zone_data()
    print(f"Loaded {len(zone_gdf)} zones")

    # Define vehicle datasets
    vehicle_datasets = [
        ("hv", f"{WORK_DIR}\\od_pl.csv", "Heavy Vehicles"),
        ("vul", f"{WORK_DIR}\\od_vul.csv", "Utility Vehicles"),
    ]

    # Process each vehicle type
    successful_count = 0
    for vehicle_code, csv_path, vehicle_name in vehicle_datasets:
        if process_vehicle_type(
            vehicle_code, csv_path, zone_gdf, output_dir, verify_checks=True
        ):
            successful_count += 1

    # Final summary
    print(f"\n" + "=" * 50)
    print(
        f"Analysis complete: {successful_count}/{len(vehicle_datasets)} vehicle types processed"
    )
    print(f"Output directory: {output_dir.absolute()}")
    print(
        f"Generated {successful_count * 6} files (3 shapefiles + 3 Excel per vehicle type)"
    )


if __name__ == "__main__":
    main()
