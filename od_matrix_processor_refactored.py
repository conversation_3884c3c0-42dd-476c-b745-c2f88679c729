"""
Refactored OD Matrix Analysis - Functional Programming Approach
Strasbourg - EMC2 Zone Grid Analysis

Simplified functional implementation with reduced code duplication and boilerplate.
"""

import pandas as pd
import geopandas as gpd
from pathlib import Path
from pyproj import Transformer
from shapely.geometry import Point
import plotly.graph_objects as go
import warnings

warnings.filterwarnings("ignore")

# Configuration
CONFIG = {
    "work_dir": r"C:\Users\<USER>\Documents\affaires\23I711.3 Strasbourg OD PL FCD",
    "output_dir": "spatial_analysis_output",
    "coordinate_bounds": (7.6, 7.9, 48.4, 48.7),  # min_lon, max_lon, min_lat, max_lat
    "max_travel_hours": 6.0,
    "target_crs": "EPSG:2154",
    "source_crs": "EPSG:4326",
    "od_categories": [
        "PASSES IN RN4_Ouest_Est",
        "PASSES IN RN4_Est_Ouest",
        "PASSES IN ROCHELLE_Sud_Nord",
        "PASSES IN ROCHELLE_Nord_Sud",
    ],
    "category_colors": {
        "PASSES IN RN4_Ouest_Est": "rgba(31, 119, 180, 0.8)",
        "PASSES IN RN4_Est_Ouest": "rgba(255, 127, 14, 0.8)",
        "PASSES IN ROCHELLE_Sud_Nord": "rgba(44, 160, 44, 0.8)",
        "PASSES IN ROCHELLE_Nord_Sud": "rgba(214, 39, 40, 0.8)",
    },
    "weekday_names": {
        0: "Monday",
        1: "Tuesday",
        2: "Wednesday",
        3: "Thursday",
        4: "Friday",
    },
    "vehicle_datasets": [
        ("hv", "od_pl.csv", "Heavy Vehicles"),
        ("vul", "od_vul.csv", "Utility Vehicles"),
    ],
}


# Pure utility functions
def clean_filename(text: str) -> str:
    """Clean text for use in filenames."""
    return text.replace(" ", "_").replace("/", "_")


def transform_coordinates(df: pd.DataFrame) -> tuple:
    """Transform coordinates from WGS84 to Lambert 93."""
    transformer = Transformer.from_crs(
        CONFIG["source_crs"], CONFIG["target_crs"], always_xy=True
    )

    origins = []
    destinations = []

    for _, row in df.iterrows():
        origin_x, origin_y = transformer.transform(row["Origine X"], row["Origine Y"])
        dest_x, dest_y = transformer.transform(
            row["Destination X"], row["Destination Y"]
        )
        origins.append(Point(origin_x, origin_y))
        destinations.append(Point(dest_x, dest_y))

    return origins, destinations


def filter_by_bounds(df: pd.DataFrame) -> pd.DataFrame:
    """Filter dataframe by coordinate bounds and travel time."""
    min_lon, max_lon, min_lat, max_lat = CONFIG["coordinate_bounds"]
    max_minutes = CONFIG["max_travel_hours"] * 60

    return df[
        (df["Origine X"] >= min_lon)
        & (df["Origine X"] <= max_lon)
        & (df["Origine Y"] >= min_lat)
        & (df["Origine Y"] <= max_lat)
        & (df["Destination X"] >= min_lon)
        & (df["Destination X"] <= max_lon)
        & (df["Destination Y"] >= min_lat)
        & (df["Destination Y"] <= max_lat)
        & (df["travel_time_minutes"] > 0)
        & (df["travel_time_minutes"] <= max_minutes)
    ].drop_duplicates(subset=["ID", "datetime"], keep="first")


# Generic data loading functions
def load_shapefile(path: str, target_crs: str = None) -> gpd.GeoDataFrame:
    """Generic shapefile loader."""
    if target_crs is None:
        target_crs = CONFIG["target_crs"]

    gdf = gpd.read_file(path)
    if gdf.crs != target_crs:
        gdf = gdf.to_crs(target_crs)
    return gdf


def load_and_parse_csv(csv_path: str) -> pd.DataFrame:
    """Load CSV and add all derived columns."""
    df = pd.read_csv(csv_path, sep=";", encoding="utf-8")

    # Parse datetime columns
    df["datetime"] = pd.to_datetime(
        df["Date"] + " " + df["Heure"], format="%d/%m/%Y %H:%M"
    )
    df["origin_datetime"] = pd.to_datetime(df["Horodate origine"])
    df["destination_datetime"] = pd.to_datetime(df["Horodate destination"])

    # Calculate travel time and temporal features
    df["travel_time_minutes"] = (
        df["destination_datetime"] - df["origin_datetime"]
    ).dt.total_seconds() / 60
    df["hour"] = df["datetime"].dt.hour
    df["weekday_num"] = df["datetime"].dt.weekday

    return df


# Unified spatial processing
def add_spatial_zones(
    df: pd.DataFrame, zone_gdf: gpd.GeoDataFrame, zone_attr: str = "NOM_DTIR"
) -> pd.DataFrame:
    """Add spatial zones to dataframe - works for both standard and D2 zones."""
    origins, destinations = transform_coordinates(df)

    # Create GeoDataFrames for spatial joins
    origins_gdf = gpd.GeoDataFrame(df.index, geometry=origins, crs=CONFIG["target_crs"])
    destinations_gdf = gpd.GeoDataFrame(
        df.index, geometry=destinations, crs=CONFIG["target_crs"]
    )

    # Perform spatial joins
    origin_zones = gpd.sjoin(
        origins_gdf, zone_gdf[[zone_attr, "geometry"]], how="left", predicate="within"
    )
    dest_zones = gpd.sjoin(
        destinations_gdf,
        zone_gdf[[zone_attr, "geometry"]],
        how="left",
        predicate="within",
    )

    # Add zone information
    df_result = df.copy()
    df_result["origin_zone"] = origin_zones[zone_attr].fillna("HorsZone")
    df_result["destination_zone"] = dest_zones[zone_attr].fillna("HorsZone")

    return df_result


# Unified OD matrix creation
def create_od_matrix(
    df: pd.DataFrame, category: str, temporal_cols: list = None
) -> pd.DataFrame:
    """Create OD matrix with optional temporal grouping."""
    # Filter by category
    category_data = df[df["OD"] == category]
    if len(category_data) == 0:
        return pd.DataFrame()

    # Apply weekday filter for daily analysis
    if temporal_cols and "weekday_num" in temporal_cols:
        category_data = category_data[category_data["weekday_num"] < 5]

    # Define grouping columns
    group_cols = ["origin_zone", "destination_zone"]
    result_cols = ["origin_zone", "destination_zone", "count", "OD_category"]

    if temporal_cols:
        group_cols.extend(temporal_cols)
        result_cols = (
            ["origin_zone", "destination_zone"]
            + temporal_cols
            + ["count", "OD_category"]
        )

    # Group and count
    od_matrix = category_data.groupby(group_cols).size().reset_index(name="count")
    od_matrix["OD_category"] = category

    # Add weekday names for daily analysis
    if temporal_cols and "weekday_num" in temporal_cols:
        od_matrix["weekday"] = od_matrix["weekday_num"].map(CONFIG["weekday_names"])
        result_cols = [
            "origin_zone",
            "destination_zone",
            "weekday",
            "count",
            "OD_category",
        ]

    return od_matrix[result_cols]


# Unified export functions
def export_data(data: pd.DataFrame, output_path: Path, format_type: str = "csv") -> str:
    """Generic export function."""
    if format_type == "csv":
        data.to_csv(output_path, index=False)
    elif format_type == "excel":
        data.to_excel(output_path, index=False)
    return str(output_path)


def export_multiple_excel(
    od_matrices: dict, vehicle_type: str, temporal_level: str, output_dir: Path
) -> str:
    """Export multiple OD matrices to Excel with sheets."""
    filename = f"{vehicle_type}_od_matrices_{temporal_level}.xlsx"
    output_path = output_dir / filename

    with pd.ExcelWriter(output_path, engine="openpyxl") as writer:
        for od_category, od_matrix in od_matrices.items():
            if not od_matrix.empty:
                sheet_name = od_category.replace("PASSES IN ", "").replace("/", "_")[
                    :31
                ]
                od_matrix.to_excel(writer, sheet_name=sheet_name, index=False)

    return str(output_path)


def create_sankey_diagram(
    od_matrix: pd.DataFrame, vehicle_type: str, od_category: str, output_dir: Path
) -> str:
    """Generate Sankey diagram from OD matrix."""
    if od_matrix.empty:
        return ""

    # Filter significant flows
    if len(od_matrix) > 50:
        threshold = od_matrix["count"].quantile(0.7)
        significant_flows = od_matrix[od_matrix["count"] > threshold]
    else:
        significant_flows = od_matrix

    # Prepare data for Sankey
    source_names = significant_flows["origin_zone"].tolist()
    target_names = significant_flows["destination_zone"].tolist()
    values = significant_flows["count"].tolist()

    all_zones = list(set(source_names + target_names))
    zone_to_index = {zone: i for i, zone in enumerate(all_zones)}

    sources = [zone_to_index[zone] for zone in source_names]
    targets = [zone_to_index[zone] for zone in target_names]

    # Create Sankey diagram
    color = CONFIG["category_colors"].get(od_category, "rgba(128, 128, 128, 0.8)")
    link_colors = [color] * len(sources)

    fig = go.Figure(
        data=[
            go.Sankey(
                node=dict(
                    pad=15,
                    thickness=20,
                    line=dict(color="black", width=0.5),
                    label=all_zones,
                    color="lightblue",
                ),
                link=dict(
                    source=sources,
                    target=targets,
                    value=values,
                    color=link_colors,
                    hovertemplate="%{source.label} → %{target.label}<br>Flow: %{value:,}<extra></extra>",
                ),
            )
        ]
    )

    title = f"{vehicle_type.upper()} - {od_category} Flow Analysis"
    fig.update_layout(title_text=title, font_size=10, height=800, width=1200)

    # Save
    category_clean = clean_filename(od_category)
    filename = f"{vehicle_type}_sankey_{category_clean}.html"
    output_path = output_dir / filename
    fig.write_html(str(output_path))

    return str(output_path)


# Processing pipeline functions
def process_vehicle_data(
    vehicle_code: str, csv_path: str, zone_gdf: gpd.GeoDataFrame, output_dir: Path
) -> dict:
    """Process single vehicle type through the pipeline."""
    full_csv_path = Path(CONFIG["work_dir"]) / csv_path

    if not full_csv_path.exists():
        print(f"  Input file not found: {full_csv_path}")
        return {}

    # Data pipeline
    df = load_and_parse_csv(str(full_csv_path))
    print(f"  Loaded {len(df):,} records")

    df = filter_by_bounds(df)
    print(f"  After validation: {len(df):,} records")

    df_with_zones = add_spatial_zones(df, zone_gdf)

    # Check available categories
    available_categories = df["OD"].unique() if "OD" in df.columns else []
    print(f"  Available OD categories: {list(available_categories)}")

    # Process temporal levels
    all_matrices = {}
    temporal_configs = [
        ("total", None),
        ("hourly", ["hour"]),
        ("daily", ["weekday_num"]),
    ]

    for temporal_level, temporal_cols in temporal_configs:
        od_matrices = {}

        for od_category in CONFIG["od_categories"]:
            if od_category in available_categories:
                od_matrix = create_od_matrix(df_with_zones, od_category, temporal_cols)

                if not od_matrix.empty:
                    od_matrices[od_category] = od_matrix

                    # Export individual CSV
                    category_clean = clean_filename(od_category)
                    filename = f"{vehicle_code}_od_matrix_{temporal_level}_{category_clean}.csv"
                    export_data(od_matrix, output_dir / filename, "csv")

                    print(
                        f"    {od_category}: {len(od_matrix)} OD pairs, {od_matrix['count'].sum()} total flows"
                    )

        if od_matrices:
            # Export combined Excel
            export_multiple_excel(od_matrices, vehicle_code, temporal_level, output_dir)
            all_matrices[temporal_level] = od_matrices

    return all_matrices


def generate_sankey_diagrams_for_vehicle(
    vehicle_code: str, csv_path: str, output_dir: Path
) -> list:
    """Generate Sankey diagrams for a vehicle type using D2 zones."""
    full_csv_path = Path(CONFIG["work_dir"]) / csv_path
    d2_zone_path = (
        Path(CONFIG["work_dir"]) / "SIG_EMC2" / "EMC2_NordAlsace_2024_D2_31072024.shp"
    )

    # Load data and D2 zones
    df = load_and_parse_csv(str(full_csv_path))
    df = filter_by_bounds(df)
    d2_zone_gdf = load_shapefile(str(d2_zone_path))
    df_with_d2_zones = add_spatial_zones(df, d2_zone_gdf, "NOM_D2")

    # Generate Sankey diagrams
    available_categories = df["OD"].unique() if "OD" in df.columns else []
    sankey_paths = []

    for od_category in CONFIG["od_categories"]:
        if od_category in available_categories:
            od_matrix = create_od_matrix(df_with_d2_zones, od_category)
            if not od_matrix.empty:
                sankey_path = create_sankey_diagram(
                    od_matrix, vehicle_code, od_category, output_dir
                )
                if sankey_path:
                    sankey_paths.append(sankey_path)

    return sankey_paths


def create_summary_report(results: dict, output_dir: Path):
    """Create comprehensive summary report."""
    summary_path = output_dir / "od_matrix_analysis_summary.txt"

    with open(summary_path, "w") as f:
        f.write("OD MATRIX ANALYSIS SUMMARY - STRASBOURG EMC2 ZONE GRID\n")
        f.write("=" * 60 + "\n\n")

        for vehicle_code, matrices in results.items():
            if matrices:
                f.write(f"{vehicle_code.upper()} PROCESSING: SUCCESS\n")
                f.write("-" * 40 + "\n")

                # Count files
                csv_files = list(output_dir.glob(f"{vehicle_code}_od_matrix_*.csv"))
                excel_files = list(
                    output_dir.glob(f"{vehicle_code}_od_matrices_*.xlsx")
                )
                sankey_files = list(output_dir.glob(f"{vehicle_code}_sankey_*.html"))

                f.write(f"Generated CSV files: {len(csv_files)}\n")
                f.write(f"Generated Excel files: {len(excel_files)}\n")
                f.write(f"Generated Sankey diagrams: {len(sankey_files)}\n\n")
            else:
                f.write(f"{vehicle_code.upper()} PROCESSING: FAILED\n")
                f.write("-" * 40 + "\n\n")


def main():
    """Main execution workflow."""
    print("OD Matrix Analysis - Strasbourg EMC2 Zone Grid")
    print("=" * 50)

    # Setup
    output_dir = Path(CONFIG["output_dir"])
    output_dir.mkdir(exist_ok=True)

    zone_path = (
        Path(CONFIG["work_dir"]) / "SIG_EMC2" / "EMC2_NordAlsace_2024_DTIR_31072024.shp"
    )
    zone_gdf = load_shapefile(str(zone_path))
    print(f"Loaded {len(zone_gdf)} zones")

    # Process each vehicle type
    results = {}
    for vehicle_code, csv_filename, vehicle_name in CONFIG["vehicle_datasets"]:
        print(f"\nProcessing {vehicle_name}...")
        matrices = process_vehicle_data(
            vehicle_code, csv_filename, zone_gdf, output_dir
        )
        results[vehicle_code] = matrices

    # Generate Sankey diagrams
    print("\n" + "=" * 50)
    print("GENERATING SANKEY DIAGRAMS")
    print("=" * 50)

    for vehicle_code, csv_filename, vehicle_name in CONFIG["vehicle_datasets"]:
        if results.get(vehicle_code):
            print(f"\nGenerating Sankey diagrams for {vehicle_name}...")
            sankey_paths = generate_sankey_diagrams_for_vehicle(
                vehicle_code, csv_filename, output_dir
            )
            print(f"  ✓ Generated {len(sankey_paths)} Sankey diagrams")

    # Create summary
    create_summary_report(results, output_dir)

    # Final summary
    successful_count = sum(1 for matrices in results.values() if matrices)
    print(f"\n" + "=" * 50)
    print(
        f"Analysis complete: {successful_count}/{len(CONFIG['vehicle_datasets'])} vehicle types processed"
    )
    print(f"Output directory: {output_dir.absolute()}")


if __name__ == "__main__":
    main()
